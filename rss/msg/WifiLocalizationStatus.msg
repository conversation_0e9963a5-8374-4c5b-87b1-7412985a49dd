# WiFi定位状态消息
# 用于在自动恢复过程中提供详细的进度反馈

# 状态枚举值
uint8 STATUS_IDLE = 0           # 空闲状态
uint8 STATUS_TRIGGERED = 1      # 已触发WiFi定位
uint8 STATUS_COLLECTING_RSS = 2 # 正在收集RSS数据
uint8 STATUS_PROCESSING = 3     # 正在处理WiFi定位
uint8 STATUS_COMPLETED = 4      # WiFi定位完成
uint8 STATUS_FAILED = 5         # WiFi定位失败

# 当前状态
uint8 status

# 进度信息
uint8 rss_data_collected        # 已收集的RSS数据点数
uint8 rss_data_required         # 需要收集的RSS数据点数
float64 progress_percentage     # 进度百分比 (0.0-100.0)

# 时间信息
builtin_interfaces/Time start_time     # 开始时间
builtin_interfaces/Time estimated_completion_time  # 预计完成时间
float64 elapsed_time           # 已用时间(秒)
float64 remaining_time         # 预计剩余时间(秒)

# 状态描述
string status_message          # 状态描述信息
string error_message           # 错误信息(如果有)

# 自动恢复模式标志
bool auto_recovery_mode        # 是否处于自动恢复模式

# 定位结果预览(仅在完成时有效)
bool has_result               # 是否有定位结果
float64 result_longitude      # 结果经度
float64 result_latitude       # 结果纬度
int32 result_floor           # 结果楼层
