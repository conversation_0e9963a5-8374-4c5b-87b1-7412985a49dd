<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>localization_using_area_graph</name>
  <version>0.0.0</version>
  <description>The localizationUsingAreaGraph package</description>
  <maintainer email="<EMAIL>">xiefujing</maintainer>
  <license>TODO</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>rosidl_default_generators</buildtool_depend>

  <depend>rclcpp</depend>
  <depend>rclcpp_lifecycle</depend>
  <depend>std_msgs</depend>
  <depend>std_srvs</depend>
  <depend>sensor_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>visualization_msgs</depend>
  <depend>builtin_interfaces</depend>
  <depend>tf2</depend>
  <depend>tf2_ros</depend>
  <depend>tf2_geometry_msgs</depend>

  <depend>cv_bridge</depend>
  <depend>image_transport</depend>
  <depend>pcl_conversions</depend>
  <depend>pcl_ros</depend>
  <depend>libpcl-all-dev</depend>
  <depend>area_graph_data_parser</depend>
  <depend>message_filters</depend>
  <depend>rss</depend>
  <depend>rosidl_default_generators</depend>
  <depend>rosidl_default_runtime</depend>
  
  <!-- New dependencies for refactored nodes -->
  <depend>yaml-cpp</depend>
  <depend>rosbag2_cpp</depend>
  <depend>rosbag2_storage</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <depend>nav2_util</depend>
  <depend>nav2_msgs</depend>
  <depend>nav2_common</depend>

  <member_of_group>rosidl_interface_packages</member_of_group>

  <export>
    <build_type>ament_cmake</build_type>
    <nav2_core plugin="${prefix}/plugins.xml"/>
  </export>
</package>