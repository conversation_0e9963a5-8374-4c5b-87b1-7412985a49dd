#!/usr/bin/env python3
"""
WiFi重定位自动恢复功能分阶段超时测试脚本
用于验证新的分阶段动态超时机制是否正常工作
"""

import rclpy
from rclpy.node import Node
from std_srvs.srv import Trigger
from rss.msg import WifiLocation
from sensor_msgs.msg import PointCloud2
import time
import threading
from datetime import datetime

class AdaptiveRecoveryTester(Node):
    def __init__(self):
        super().__init__('adaptive_recovery_tester')
        
        # 测试状态
        self.test_results = {
            'wifi_service_trigger': False,
            'wifi_location_received': False,
            'particle_service_trigger': False,
            'particles_received': False,
            'recovery_completion': False
        }
        
        self.start_time = None
        self.phase_times = {}
        
        # 订阅关键话题
        self.wifi_location_sub = self.create_subscription(
            WifiLocation, '/WifiLocation', self.wifi_location_callback, 10)
        
        self.particles_sub = self.create_subscription(
            PointCloud2, '/particles_for_init', self.particles_callback, 10)
        
        # 创建服务客户端
        self.wifi_client = self.create_client(<PERSON>gger, 'wifi_loc/trigger_global_localization')
        self.particle_client = self.create_client(Trigger, 'particle_generator/reset_and_regenerate')
        
        self.get_logger().info('🧪 分阶段WiFi恢复测试器已初始化')
    
    def wifi_location_callback(self, msg):
        if not self.test_results['wifi_location_received']:
            self.test_results['wifi_location_received'] = True
            self.phase_times['wifi_location'] = time.time() - self.start_time
            self.get_logger().info(f'✅ 收到WiFi定位结果，用时: {self.phase_times["wifi_location"]:.2f}秒')
    
    def particles_callback(self, msg):
        if not self.test_results['particles_received']:
            self.test_results['particles_received'] = True
            self.phase_times['particles'] = time.time() - self.start_time
            self.get_logger().info(f'✅ 收到粒子数据，用时: {self.phase_times["particles"]:.2f}秒')
    
    def test_adaptive_recovery_flow(self):
        """测试完整的分阶段恢复流程"""
        self.get_logger().info('🚀 开始测试分阶段WiFi恢复流程')
        self.start_time = time.time()
        
        # 阶段1: 测试WiFi服务触发
        if not self.test_wifi_service():
            return False
        
        # 阶段2: 等待WiFi定位完成
        if not self.wait_for_wifi_location():
            return False
        
        # 阶段3: 测试粒子生成服务触发
        if not self.test_particle_service():
            return False
        
        # 阶段4: 等待粒子生成完成
        if not self.wait_for_particles():
            return False
        
        self.get_logger().info('🎉 分阶段WiFi恢复流程测试完成')
        self.print_test_summary()
        return True
    
    def test_wifi_service(self):
        """测试WiFi定位服务触发"""
        self.get_logger().info('📡 测试阶段1: WiFi定位服务触发')
        
        if not self.wifi_client.wait_for_service(timeout_sec=5.0):
            self.get_logger().error('❌ WiFi定位服务不可用')
            return False
        
        request = Trigger.Request()
        future = self.wifi_client.call_async(request)
        
        start_time = time.time()
        while not future.done() and (time.time() - start_time) < 10.0:
            rclpy.spin_once(self, timeout_sec=0.1)
        
        if future.done():
            response = future.result()
            if response.success:
                self.test_results['wifi_service_trigger'] = True
                self.phase_times['wifi_service'] = time.time() - self.start_time
                self.get_logger().info(f'✅ WiFi服务触发成功，用时: {self.phase_times["wifi_service"]:.2f}秒')
                return True
        
        self.get_logger().error('❌ WiFi服务触发失败')
        return False
    
    def wait_for_wifi_location(self):
        """等待WiFi定位完成"""
        self.get_logger().info('⏳ 等待WiFi定位完成...')
        
        timeout = 15.0  # 给WiFi定位足够的时间
        start_wait = time.time()
        
        while not self.test_results['wifi_location_received'] and (time.time() - start_wait) < timeout:
            rclpy.spin_once(self, timeout_sec=0.1)
        
        return self.test_results['wifi_location_received']
    
    def test_particle_service(self):
        """测试粒子生成服务触发"""
        self.get_logger().info('🔄 测试阶段3: 粒子生成服务触发')
        
        if not self.particle_client.wait_for_service(timeout_sec=5.0):
            self.get_logger().error('❌ 粒子生成服务不可用')
            return False
        
        request = Trigger.Request()
        future = self.particle_client.call_async(request)
        
        start_time = time.time()
        while not future.done() and (time.time() - start_time) < 5.0:
            rclpy.spin_once(self, timeout_sec=0.1)
        
        if future.done():
            response = future.result()
            if response.success:
                self.test_results['particle_service_trigger'] = True
                self.phase_times['particle_service'] = time.time() - self.start_time
                self.get_logger().info(f'✅ 粒子服务触发成功，用时: {self.phase_times["particle_service"]:.2f}秒')
                return True
        
        self.get_logger().error('❌ 粒子服务触发失败')
        return False
    
    def wait_for_particles(self):
        """等待粒子生成完成"""
        self.get_logger().info('⏳ 等待粒子生成完成...')
        
        timeout = 10.0  # 给粒子生成足够的时间
        start_wait = time.time()
        
        while not self.test_results['particles_received'] and (time.time() - start_wait) < timeout:
            rclpy.spin_once(self, timeout_sec=0.1)
        
        return self.test_results['particles_received']
    
    def print_test_summary(self):
        """打印测试结果摘要"""
        total_time = time.time() - self.start_time
        
        self.get_logger().info('📊 测试结果摘要:')
        self.get_logger().info(f'   总用时: {total_time:.2f}秒')
        self.get_logger().info(f'   WiFi服务触发: {"✅" if self.test_results["wifi_service_trigger"] else "❌"} ({self.phase_times.get("wifi_service", 0):.2f}s)')
        self.get_logger().info(f'   WiFi定位完成: {"✅" if self.test_results["wifi_location_received"] else "❌"} ({self.phase_times.get("wifi_location", 0):.2f}s)')
        self.get_logger().info(f'   粒子服务触发: {"✅" if self.test_results["particle_service_trigger"] else "❌"} ({self.phase_times.get("particle_service", 0):.2f}s)')
        self.get_logger().info(f'   粒子生成完成: {"✅" if self.test_results["particles_received"] else "❌"} ({self.phase_times.get("particles", 0):.2f}s)')

def main():
    rclpy.init()
    tester = AdaptiveRecoveryTester()
    
    try:
        # 等待系统稳定
        time.sleep(2.0)
        
        # 运行测试
        success = tester.test_adaptive_recovery_flow()
        
        if success:
            tester.get_logger().info('🎉 所有测试通过!')
        else:
            tester.get_logger().error('❌ 测试失败!')
            
    except KeyboardInterrupt:
        tester.get_logger().info('测试被用户中断')
    finally:
        tester.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main() 