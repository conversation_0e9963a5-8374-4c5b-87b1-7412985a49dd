#!/usr/bin/env python3
"""
AGLoc自动恢复服务测试脚本
测试WiFi全局定位和粒子生成器的服务接口
"""

import rclpy
from rclpy.node import Node
from std_srvs.srv import Trigger
from localization_using_area_graph.msg import AutoRecoveryStatus
import time

class RecoveryServiceTester(Node):
    def __init__(self):
        super().__init__('recovery_service_tester')
        
        # 创建服务客户端
        self.wifi_client = self.create_client(Trigger, 'wifi_loc/trigger_global_localization')
        self.particle_client = self.create_client(Trigger, 'particle_generator/reset_and_regenerate')
        
        # 创建状态订阅者
        self.status_sub = self.create_subscription(
            AutoRecoveryStatus,
            'auto_recovery_status',
            self.status_callback,
            10
        )
        
        self.get_logger().info('🔧 AGLoc自动恢复服务测试器已启动')
        
    def status_callback(self, msg):
        """自动恢复状态回调"""
        status_names = {
            0: 'IDLE',
            1: 'WIFI_LOCALIZING', 
            2: 'PARTICLE_GENERATING',
            3: 'RECOVERY_SUCCESS',
            4: 'RECOVERY_FAILED'
        }
        
        status_name = status_names.get(msg.status, 'UNKNOWN')
        self.get_logger().info(f'📊 自动恢复状态: {status_name}')
        self.get_logger().info(f'   恢复尝试次数: {msg.recovery_attempts}')
        self.get_logger().info(f'   失败检测计数: {msg.failure_detection_count}')
        if msg.debug_info:
            self.get_logger().info(f'   调试信息: {msg.debug_info}')
    
    def test_wifi_service(self):
        """测试WiFi全局定位服务"""
        self.get_logger().info('🌐 测试WiFi全局定位服务...')
        
        if not self.wifi_client.wait_for_service(timeout_sec=5.0):
            self.get_logger().error('❌ WiFi定位服务不可用')
            return False
            
        request = Trigger.Request()
        future = self.wifi_client.call_async(request)
        
        rclpy.spin_until_future_complete(self, future, timeout_sec=10.0)
        
        if future.result() is not None:
            response = future.result()
            if response.success:
                self.get_logger().info(f'✅ WiFi定位服务调用成功: {response.message}')
                return True
            else:
                self.get_logger().error(f'❌ WiFi定位服务调用失败: {response.message}')
                return False
        else:
            self.get_logger().error('⏰ WiFi定位服务调用超时')
            return False
    
    def test_particle_service(self):
        """测试粒子生成器重置服务"""
        self.get_logger().info('🎯 测试粒子生成器重置服务...')
        
        if not self.particle_client.wait_for_service(timeout_sec=5.0):
            self.get_logger().error('❌ 粒子生成器服务不可用')
            return False
            
        request = Trigger.Request()
        future = self.particle_client.call_async(request)
        
        rclpy.spin_until_future_complete(self, future, timeout_sec=10.0)
        
        if future.result() is not None:
            response = future.result()
            if response.success:
                self.get_logger().info(f'✅ 粒子生成器服务调用成功: {response.message}')
                return True
            else:
                self.get_logger().error(f'❌ 粒子生成器服务调用失败: {response.message}')
                return False
        else:
            self.get_logger().error('⏰ 粒子生成器服务调用超时')
            return False
    
    def run_tests(self):
        """运行所有测试"""
        self.get_logger().info('🚀 开始AGLoc自动恢复服务测试')

        # 等待一下让系统稳定
        time.sleep(2.0)

        # 测试WiFi服务
        wifi_ok = self.test_wifi_service()
        time.sleep(5.0)  # 增加等待时间，让WiFi定位有时间完成

        # 测试粒子生成器服务
        particle_ok = self.test_particle_service()
        time.sleep(3.0)  # 增加等待时间，让粒子生成有时间完成

        # 输出测试结果
        self.get_logger().info('=' * 60)
        self.get_logger().info('📋 测试结果总结:')
        self.get_logger().info('=' * 60)
        self.get_logger().info(f'🌐 WiFi定位服务: {"✅ 通过" if wifi_ok else "❌ 失败"}')
        self.get_logger().info(f'🎯 粒子生成器服务: {"✅ 通过" if particle_ok else "❌ 失败"}')

        if wifi_ok and particle_ok:
            self.get_logger().info('🎉 所有服务测试通过！AGLoc自动恢复功能已就绪')
            self.get_logger().info('💡 提示: WiFi定位需要收集RSS数据，实际恢复过程需要10-15秒')
            self.get_logger().info('💡 提示: 粒子生成需要WiFi定位完成后才能进行')
        else:
            self.get_logger().error('⚠️  部分服务测试失败，请检查相关节点是否正常运行')
            self.get_logger().error('💡 故障排除提示:')
            self.get_logger().error('   1. 确保robot_loc节点正在运行并能接收RSS数据')
            self.get_logger().error('   2. 确保particle_generator节点已加载地图数据')
            self.get_logger().error('   3. 检查QoS设置是否匹配(TRANSIENT_LOCAL)')

        self.get_logger().info('=' * 60)

def main(args=None):
    rclpy.init(args=args)
    
    tester = RecoveryServiceTester()
    
    try:
        # 运行测试
        tester.run_tests()
        
        # 继续监听状态消息
        tester.get_logger().info('👂 继续监听自动恢复状态消息...')
        tester.get_logger().info('   按Ctrl+C停止监听')
        rclpy.spin(tester)
        
    except KeyboardInterrupt:
        tester.get_logger().info('🛑 测试被用户中断')
    finally:
        tester.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
