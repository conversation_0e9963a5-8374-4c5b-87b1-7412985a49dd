#include "localization_using_area_graph/auto_recovery.hpp"
#include <algorithm>
#include <cmath>

namespace auto_recovery {

// ============================================================================
// FailureDetector Implementation
// ============================================================================

void FailureDetector::updateQuality(const TrackingQuality& quality) {
    // 添加到历史记录
    quality_history_.push_back(quality);
    if (quality_history_.size() > max_history_size_) {
        quality_history_.erase(quality_history_.begin());
    }
    
    // 检查即时失败
    if (isCriticalFailure(quality)) {
        critical_failure_count_++;
        RCLCPP_WARN(rclcpp::get_logger("FailureDetector"),
                   "检测到严重失败 #%d/%d: ICP点数=%d(阈值<%d), 权重=%.6f(阈值<%.3f), 平移=%.3f(阈值>%.1f)",
                   critical_failure_count_, config_.max_critical_failures,
                   quality.numIcpPoints, config_.min_icp_points,
                   quality.weightSumTurkey, config_.min_weight_sum,
                   quality.translationNorm, config_.max_translation_norm);
    } else {
        if (critical_failure_count_ > 0) {
            RCLCPP_INFO(rclcpp::get_logger("FailureDetector"), "严重失败计数已重置");
        }
        critical_failure_count_ = 0;  // 重置严重失败计数
    }
    
    // 检查质量退化
    if (isQualityDegraded(quality)) {
        quality_failure_count_++;
        consecutive_poor_frames_++;
        RCLCPP_DEBUG(rclcpp::get_logger("FailureDetector"),
                    "检测到质量退化 #%d/%d (连续%d/%d): ICP得分=%.3f(阈值>%.2f)",
                    quality_failure_count_, config_.max_quality_failures,
                    consecutive_poor_frames_, config_.max_consecutive_poor,
                    quality.icpScore, config_.min_icp_score);
    } else {
        if (quality_failure_count_ > 0 || consecutive_poor_frames_ > 0) {
            RCLCPP_DEBUG(rclcpp::get_logger("FailureDetector"), "质量失败计数已重置");
        }
        quality_failure_count_ = 0;  // 重置质量失败计数
        consecutive_poor_frames_ = 0;  // 重置连续质量差计数
    }
}

bool FailureDetector::shouldTriggerRecovery() const {
    bool should_trigger = (critical_failure_count_ >= config_.max_critical_failures) ||
                         (quality_failure_count_ >= config_.max_quality_failures) ||
                         (consecutive_poor_frames_ >= config_.max_consecutive_poor);
    
    if (should_trigger) {
        RCLCPP_WARN(rclcpp::get_logger("FailureDetector"), 
                   "触发恢复条件满足: 严重失败=%d/%d, 质量失败=%d/%d, 连续质量差=%d/%d",
                   critical_failure_count_, config_.max_critical_failures,
                   quality_failure_count_, config_.max_quality_failures,
                   consecutive_poor_frames_, config_.max_consecutive_poor);
    }
    
    return should_trigger;
}

void FailureDetector::reset() {
    critical_failure_count_ = 0;
    quality_failure_count_ = 0;
    consecutive_poor_frames_ = 0;
    quality_history_.clear();
    
    RCLCPP_INFO(rclcpp::get_logger("FailureDetector"), "失败检测器已重置");
}

void FailureDetector::getFailureStats(int& critical, int& quality, int& consecutive) const {
    critical = critical_failure_count_;
    quality = quality_failure_count_;
    consecutive = consecutive_poor_frames_;
}

bool FailureDetector::isCriticalFailure(const TrackingQuality& quality) const {
    // 添加数据有效性检查，避免在数据刚重置时误报
    bool has_valid_data = (quality.numIcpPoints >= 0) &&
                         (quality.weightSumTurkey >= 0) &&
                         (quality.translationNorm >= 0);

    if (!has_valid_data) {
        return false;  // 数据无效，不认为是失败
    }

    // 只有在所有条件都满足时才认为是严重失败
    bool points_too_few = (quality.numIcpPoints < config_.min_icp_points);
    bool weight_too_low = (quality.weightSumTurkey < config_.min_weight_sum);
    bool translation_too_large = (quality.translationNorm > config_.max_translation_norm);

    // 需要至少两个条件同时满足才认为是严重失败，避免单一指标误报
    int failure_count = (points_too_few ? 1 : 0) + (weight_too_low ? 1 : 0) + (translation_too_large ? 1 : 0);

    return failure_count >= 2;
}

bool FailureDetector::isQualityDegraded(const TrackingQuality& quality) const {
    // 添加数据有效性检查
    if (quality.numIcpPoints < 0 || quality.weightSumTurkey < 0) {
        return false;  // 数据无效，不认为是质量退化
    }

    // 计算点数比例（假设总点数为64*600）
    double point_ratio = static_cast<double>(quality.numIcpPoints) / (64.0 * 600.0);

    // 计算平均权重比例
    double weight_ratio = quality.numIcpPoints > 0 ?
                         (quality.weightSumTurkey / quality.numIcpPoints) : 0.0;

    // 只有在多个指标都不好时才认为是质量退化
    bool points_low = (point_ratio < config_.min_point_ratio);
    bool weight_low = (weight_ratio < config_.min_weight_ratio);
    bool score_low = (quality.icpScore < config_.min_icp_score);

    // 需要至少两个条件同时满足才认为是质量退化
    int degradation_count = (points_low ? 1 : 0) + (weight_low ? 1 : 0) + (score_low ? 1 : 0);

    return degradation_count >= 2;
}

// ============================================================================
// AutoRecoveryStateMachine Implementation
// ============================================================================

AutoRecoveryStateMachine::AutoRecoveryStateMachine(const Config& config, 
                                                 const FailureDetector::Config& detector_config,
                                                 rclcpp::Logger logger)
    : config_(config), 
      current_state_(LocalizationState::ICP_TRACKING),
      logger_(logger) {
    
    failure_detector_ = std::make_unique<FailureDetector>(detector_config);
    recovery_start_time_ = rclcpp::Time(0);
    last_state_change_ = rclcpp::Time(0);
    
    RCLCPP_INFO(logger_, "自动恢复状态机已初始化");
}

void AutoRecoveryStateMachine::update(const TrackingQuality& quality, rclcpp::Time current_time) {
    // 更新失败检测器
    failure_detector_->updateQuality(quality);
    
    LocalizationState old_state = current_state_;
    
    switch (current_state_) {
        case LocalizationState::ICP_TRACKING:
            if (failure_detector_->shouldTriggerRecovery()) {
                changeState(LocalizationState::FAILURE_DETECTED, current_time);
            }
            break;
            
        case LocalizationState::FAILURE_DETECTED:
            if (recovery_attempts_ < config_.max_recovery_attempts) {
                changeState(LocalizationState::WIFI_RECOVERY, current_time);
                recovery_start_time_ = current_time;
                recovery_attempts_++;
            } else {
                RCLCPP_ERROR(logger_, "❌ 达到最大恢复尝试次数 (%d)，停止自动恢复", 
                           config_.max_recovery_attempts);
                // 保持在FAILURE_DETECTED状态，等待手动干预
            }
            break;
            
        case LocalizationState::WIFI_RECOVERY:
            if (isRecoveryTimeout(current_time)) {
                RCLCPP_WARN(logger_, "⏰ WiFi恢复超时，重新尝试");
                changeState(LocalizationState::FAILURE_DETECTED, current_time);
            }
            // WiFi恢复完成由外部通知
            break;
            
        case LocalizationState::RECOVERY_VALIDATION:
            // 验证逻辑由外部处理，这里只处理超时
            if (isRecoveryTimeout(current_time)) {
                RCLCPP_WARN(logger_, "⏰ 恢复验证超时，重新尝试");
                changeState(LocalizationState::FAILURE_DETECTED, current_time);
            }
            break;
            
        case LocalizationState::SMOOTH_TRANSITION:
            // 过渡逻辑由外部处理
            break;
    }
    
    if (old_state != current_state_) {
        logStateChange(old_state, current_state_);
    }
}

bool AutoRecoveryStateMachine::shouldTriggerWiFiRecovery() const {
    return current_state_ == LocalizationState::WIFI_RECOVERY;
}

bool AutoRecoveryStateMachine::shouldValidateRecovery() const {
    return current_state_ == LocalizationState::RECOVERY_VALIDATION;
}

bool AutoRecoveryStateMachine::shouldPerformTransition() const {
    return current_state_ == LocalizationState::SMOOTH_TRANSITION;
}

void AutoRecoveryStateMachine::notifyWiFiRecoveryComplete(double recovery_score) {
    if (current_state_ == LocalizationState::WIFI_RECOVERY) {
        if (recovery_score >= config_.min_recovery_score) {
            RCLCPP_INFO(logger_, "✅ WiFi恢复完成，得分: %.3f", recovery_score);
            changeState(LocalizationState::RECOVERY_VALIDATION, rclcpp::Clock().now());
        } else {
            RCLCPP_WARN(logger_, "⚠️  WiFi恢复得分过低: %.3f < %.3f", 
                       recovery_score, config_.min_recovery_score);
            changeState(LocalizationState::FAILURE_DETECTED, rclcpp::Clock().now());
        }
    }
}

void AutoRecoveryStateMachine::notifyTransitionComplete() {
    if (current_state_ == LocalizationState::SMOOTH_TRANSITION) {
        RCLCPP_INFO(logger_, "🎯 平滑过渡完成，恢复正常跟踪");
        changeState(LocalizationState::ICP_TRACKING, rclcpp::Clock().now());
        recovery_attempts_ = 0;  // 重置恢复尝试计数
        failure_detector_->reset();  // 重置失败检测器
    }
}

void AutoRecoveryStateMachine::reset() {
    current_state_ = LocalizationState::ICP_TRACKING;
    recovery_attempts_ = 0;
    transition_frame_count_ = 0;
    failure_detector_->reset();
    
    RCLCPP_INFO(logger_, "自动恢复状态机已重置");
}

void AutoRecoveryStateMachine::getRecoveryStats(int& attempts, double& success_rate) const {
    attempts = recovery_attempts_;
    // 简单的成功率计算，实际应用中可以更复杂
    success_rate = recovery_attempts_ > 0 ? 
                  (current_state_ == LocalizationState::ICP_TRACKING ? 1.0 : 0.0) : 0.0;
}

void AutoRecoveryStateMachine::changeState(LocalizationState new_state, rclcpp::Time current_time) {
    current_state_ = new_state;
    last_state_change_ = current_time;
}

bool AutoRecoveryStateMachine::isRecoveryTimeout(rclcpp::Time current_time) const {
    if (recovery_start_time_.seconds() == 0) {
        return false;  // 还没开始恢复
    }
    
    double elapsed = (current_time - recovery_start_time_).seconds();
    return elapsed > config_.recovery_timeout;
}

void AutoRecoveryStateMachine::logStateChange(LocalizationState old_state, LocalizationState new_state) const {
    auto state_to_string = [](LocalizationState state) -> std::string {
        switch (state) {
            case LocalizationState::ICP_TRACKING: return "ICP_TRACKING";
            case LocalizationState::FAILURE_DETECTED: return "FAILURE_DETECTED";
            case LocalizationState::WIFI_RECOVERY: return "WIFI_RECOVERY";
            case LocalizationState::RECOVERY_VALIDATION: return "RECOVERY_VALIDATION";
            case LocalizationState::SMOOTH_TRANSITION: return "SMOOTH_TRANSITION";
            default: return "UNKNOWN";
        }
    };
    
    if (config_.enable_debug_output) {
        RCLCPP_INFO(logger_, "🔄 状态转换: %s -> %s", 
                   state_to_string(old_state).c_str(), 
                   state_to_string(new_state).c_str());
    }
}

// WiFi恢复阶段管理方法实现
void AutoRecoveryStateMachine::setWiFiRecoveryPhase(WiFiRecoveryPhase phase, rclcpp::Time current_time) {
    if (current_wifi_phase_ != phase) {
        RCLCPP_INFO(logger_, "🔄 WiFi恢复阶段转换: %s -> %s", 
                   wifiPhaseToString(current_wifi_phase_).c_str(),
                   wifiPhaseToString(phase).c_str());
    }
    
    current_wifi_phase_ = phase;
    wifi_phase_start_time_ = current_time;
    last_wifi_status_check_ = current_time;
}



bool AutoRecoveryStateMachine::isPhaseTimeout(WiFiRecoveryPhase phase, rclcpp::Time current_time) const {
    if (wifi_phase_start_time_.seconds() == 0) {
        return false;  // 阶段还没开始
    }
    
    double elapsed = (current_time - wifi_phase_start_time_).seconds();
    double timeout = getPhaseTimeout(phase);
    
    return elapsed > timeout;
}

double AutoRecoveryStateMachine::getPhaseTimeout(WiFiRecoveryPhase phase) const {
    if (!config_.adaptive_timeout.enable) {
        return config_.recovery_timeout;  // 使用传统超时
    }
    
    switch (phase) {
        case WiFiRecoveryPhase::COLLECTING_RSS_DATA:
        case WiFiRecoveryPhase::WAITING_WIFI_LOCATION:
            return config_.adaptive_timeout.wifi_collection_timeout;
        case WiFiRecoveryPhase::GENERATING_PARTICLES:
            return config_.adaptive_timeout.particle_generation_timeout;
        case WiFiRecoveryPhase::EXECUTING_GLOBAL_LOC:
            return config_.adaptive_timeout.global_localization_timeout;
        case WiFiRecoveryPhase::VALIDATING_RESULT:
            return config_.adaptive_timeout.validation_timeout;
        default:
            return config_.adaptive_timeout.total_recovery_timeout;
    }
}

std::string AutoRecoveryStateMachine::wifiPhaseToString(WiFiRecoveryPhase phase) const {
    switch (phase) {
        case WiFiRecoveryPhase::IDLE: return "IDLE";
        case WiFiRecoveryPhase::TRIGGERING_WIFI_SERVICE: return "TRIGGERING_WIFI_SERVICE";
        case WiFiRecoveryPhase::COLLECTING_RSS_DATA: return "COLLECTING_RSS_DATA";
        case WiFiRecoveryPhase::WAITING_WIFI_LOCATION: return "WAITING_WIFI_LOCATION";
        case WiFiRecoveryPhase::TRIGGERING_PARTICLE_SERVICE: return "TRIGGERING_PARTICLE_SERVICE";
        case WiFiRecoveryPhase::GENERATING_PARTICLES: return "GENERATING_PARTICLES";
        case WiFiRecoveryPhase::EXECUTING_GLOBAL_LOC: return "EXECUTING_GLOBAL_LOC";
        case WiFiRecoveryPhase::VALIDATING_RESULT: return "VALIDATING_RESULT";
        case WiFiRecoveryPhase::RECOVERY_COMPLETE: return "RECOVERY_COMPLETE";
        case WiFiRecoveryPhase::RECOVERY_FAILED: return "RECOVERY_FAILED";
        default: return "UNKNOWN";
    }
}

double AutoRecoveryStateMachine::getCurrentPhaseTimeout() const {
    return getPhaseTimeout(current_wifi_phase_);
}

bool AutoRecoveryStateMachine::isCurrentPhaseTimeout(rclcpp::Time current_time) const {
    return isPhaseTimeout(current_wifi_phase_, current_time);
}

// ============================================================================
// AutoRecoveryManager Implementation
// ============================================================================

AutoRecoveryManager::AutoRecoveryManager(const Config& config, rclcpp::Logger logger)
    : config_(config), logger_(logger), enabled_(config.enable_auto_recovery) {

    if (enabled_) {
        state_machine_ = std::make_unique<AutoRecoveryStateMachine>(
            config.state_machine_config, config.detector_config, logger);

        RCLCPP_INFO(logger_, "✅ 自动恢复管理器已启用");
    } else {
        RCLCPP_INFO(logger_, "ℹ️  自动恢复管理器已禁用");
    }
}

void AutoRecoveryManager::updateTracking(int numIcpPoints, double weightSumTurkey,
                                        double translationNorm, double icpScore,
                                        rclcpp::Time timestamp) {
    if (!enabled_ || !state_machine_) {
        return;
    }

    // 构建跟踪质量结构体
    TrackingQuality quality;
    quality.numIcpPoints = numIcpPoints;
    quality.weightSumTurkey = weightSumTurkey;
    quality.translationNorm = translationNorm;
    quality.icpScore = icpScore;
    quality.timestamp = timestamp;

    // 更新状态机
    state_machine_->update(quality, timestamp);
}

LocalizationState AutoRecoveryManager::getCurrentState() const {
    if (!enabled_ || !state_machine_) {
        return LocalizationState::ICP_TRACKING;
    }
    return state_machine_->getCurrentState();
}

bool AutoRecoveryManager::shouldTriggerWiFiRecovery() const {
    if (!enabled_ || !state_machine_) {
        return false;
    }
    return state_machine_->shouldTriggerWiFiRecovery();
}

bool AutoRecoveryManager::shouldValidateRecovery() const {
    if (!enabled_ || !state_machine_) {
        return false;
    }
    return state_machine_->shouldValidateRecovery();
}

bool AutoRecoveryManager::shouldPerformTransition() const {
    if (!enabled_ || !state_machine_) {
        return false;
    }
    return state_machine_->shouldPerformTransition();
}

void AutoRecoveryManager::notifyWiFiRecoveryComplete(double recovery_score) {
    if (enabled_ && state_machine_) {
        state_machine_->notifyWiFiRecoveryComplete(recovery_score);
    }
}

void AutoRecoveryManager::notifyTransitionComplete() {
    if (enabled_ && state_machine_) {
        state_machine_->notifyTransitionComplete();
    }
}

void AutoRecoveryManager::reset() {
    if (enabled_ && state_machine_) {
        state_machine_->reset();
        RCLCPP_INFO(logger_, "自动恢复管理器已重置");
    }
}

void AutoRecoveryManager::getRecoveryStats(int& attempts, double& success_rate) const {
    if (enabled_ && state_machine_) {
        state_machine_->getRecoveryStats(attempts, success_rate);
    } else {
        attempts = 0;
        success_rate = 0.0;
    }
}

// WiFi恢复阶段管理方法实现
void AutoRecoveryManager::setWiFiRecoveryPhase(WiFiRecoveryPhase phase, rclcpp::Time current_time) {
    if (enabled_ && state_machine_) {
        state_machine_->setWiFiRecoveryPhase(phase, current_time);
    }
}

WiFiRecoveryPhase AutoRecoveryManager::getCurrentWiFiPhase() const {
    if (enabled_ && state_machine_) {
        return state_machine_->getCurrentWiFiPhase();
    }
    return WiFiRecoveryPhase::IDLE;
}

bool AutoRecoveryManager::isCurrentPhaseTimeout(rclcpp::Time current_time) const {
    if (enabled_ && state_machine_) {
        return state_machine_->isCurrentPhaseTimeout(current_time);
    }
    return false;
}

} // namespace auto_recovery
