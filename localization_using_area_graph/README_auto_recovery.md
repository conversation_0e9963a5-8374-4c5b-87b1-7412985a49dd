# AGLoc自动恢复功能使用指南

## 概述

AGLoc自动恢复功能是一个智能的位姿恢复系统，当ICP跟踪失败时自动触发WiFi全局定位来恢复机器人位姿。该功能通过多节点协调工作，提供无缝的定位恢复体验。

## 核心组件

### 1. 主要节点
- **cloud_handler**: AGLoc主节点，负责ICP跟踪和自动恢复逻辑
- **particle_generator**: 粒子生成器，为WiFi定位提供初始粒子
- **robot_loc**: WiFi定位节点，执行基于RSS的全局定位

### 2. 服务接口
- `wifi_loc/trigger_global_localization`: 触发WiFi全局定位
- `particle_generator/reset_and_regenerate`: 重置并重新生成粒子

### 3. 消息类型
- `AutoRecoveryStatus`: 自动恢复状态消息

## 配置参数

在`config/params.yaml`中配置自动恢复参数：

```yaml
/**:
    ros__parameters:
        # 自动恢复总开关
        enable_auto_recovery: true
        
        # 自动恢复详细配置
        auto_recovery:
            # 失败检测阈值
            min_icp_points: 100              # ICP最少点数阈值
            min_weight_sum: 0.1              # 最小权重和阈值
            max_translation_norm: 2.0        # 最大平移范数阈值
            
            # 失败计数阈值
            max_critical_failures: 3         # 最大严重失败次数
            max_quality_failures: 5          # 最大质量失败次数
            max_consecutive_poor: 10         # 最大连续质量差次数
            
            # 恢复配置
            recovery_timeout: 30.0           # 恢复超时时间(秒)
            min_recovery_score: 0.5          # 最小恢复得分阈值
            transition_frames: 5             # 平滑过渡帧数
            
            # 调试输出
            enable_debug_output: true        # 是否启用调试输出
            
            # 服务超时配置
            wifi_service_timeout: 10.0       # WiFi定位服务超时时间(秒)
            particle_service_timeout: 5.0    # 粒子生成服务超时时间(秒)
```

## 使用方法

### 1. 编译系统
```bash
cd /home/<USER>/AGLoc_ws
colcon build --packages-select localization_using_area_graph wifi_loc --symlink-install
source install/setup.bash
```

### 2. 启动自动恢复功能
```bash
# 启动完整的自动恢复测试
ros2 launch localization_using_area_graph test_auto_recovery.launch.py

# 或者分别启动各个节点
ros2 run localization_using_area_graph cloud_handler
ros2 run localization_using_area_graph particle_generator  
ros2 run wifi_loc robot_loc
```

### 3. 测试服务接口
```bash
# 测试自动恢复服务
python3 src/localization_using_area_graph/scripts/test_recovery_services.py

# 测试整体功能
python3 src/localization_using_area_graph/scripts/test_auto_recovery.py
```

### 4. 手动触发恢复
```bash
# 手动触发WiFi全局定位
ros2 service call /wifi_loc/trigger_global_localization std_srvs/srv/Trigger

# 手动重置粒子生成器
ros2 service call /particle_generator/reset_and_regenerate std_srvs/srv/Trigger
```

## 工作流程

1. **正常跟踪**: cloud_handler使用ICP算法进行位姿跟踪
2. **失败检测**: 监控ICP质量指标，检测跟踪失败
3. **自动触发**: 当失败次数超过阈值时，自动触发恢复流程
4. **WiFi定位**: 调用WiFi定位服务获取全局位姿估计
5. **粒子生成**: 重置并生成新的粒子用于定位
6. **位姿恢复**: 使用WiFi定位结果恢复机器人位姿
7. **平滑过渡**: 平滑切换回ICP跟踪模式

## 状态监控

订阅`/auto_recovery_status`话题监控恢复状态：

```bash
ros2 topic echo /auto_recovery_status
```

状态值说明：
- `0`: IDLE - 空闲状态
- `1`: WIFI_LOCALIZING - WiFi定位中
- `2`: PARTICLE_GENERATING - 粒子生成中  
- `3`: RECOVERY_SUCCESS - 恢复成功
- `4`: RECOVERY_FAILED - 恢复失败

## 故障排除

### 1. 服务不可用
```bash
# 检查服务是否存在
ros2 service list | grep -E "(trigger_global_localization|reset_and_regenerate)"

# 检查节点是否运行
ros2 node list
```

### 2. 恢复失败
- 检查WiFi信号强度和RSS数据质量
- 确认地图文件正确加载
- 调整恢复阈值参数

### 3. 调试输出
启用调试输出查看详细信息：
```yaml
auto_recovery:
    enable_debug_output: true
```

## 性能优化

1. **阈值调整**: 根据实际环境调整失败检测阈值
2. **超时设置**: 根据网络条件调整服务超时时间
3. **过渡平滑**: 调整transition_frames参数优化切换效果

## 向后兼容

自动恢复功能默认禁用，通过`enable_auto_recovery: false`可完全关闭，确保与现有系统的兼容性。

## 注意事项

1. 确保WiFi定位节点正常运行并能接收RSS数据
2. 粒子生成器需要正确的地图和区域信息
3. 自动恢复可能需要几秒钟时间完成
4. 在GPS拒止环境中，WiFi定位是唯一的全局定位手段

## 相关文件

- `src/cloudHandler.cpp`: 主要恢复逻辑
- `src/particle_generator.cpp`: 粒子生成和重置
- `wifi_loc/robot_loc.py`: WiFi定位服务
- `msg/AutoRecoveryStatus.msg`: 状态消息定义
- `config/params.yaml`: 配置参数
- `scripts/test_*.py`: 测试脚本
