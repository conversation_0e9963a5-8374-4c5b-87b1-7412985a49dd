# 自动恢复状态消息
# 用于发布自动恢复流程的详细状态信息

# 时间戳
builtin_interfaces/Time timestamp

# 恢复状态枚举
uint8 IDLE = 0
uint8 DETECTING_FAILURE = 1
uint8 TRIGGERING_WIFI = 2
uint8 WAITING_WIFI_RESULT = 3
uint8 TRIGGERING_PARTICLES = 4
uint8 WAITING_PARTICLES = 5
uint8 EXECUTING_GLOBAL_LOC = 6
uint8 VALIDATING_RESULT = 7
uint8 SMOOTH_TRANSITION = 8
uint8 RECOVERY_COMPLETE = 9
uint8 RECOVERY_FAILED = 10

uint8 status

# 恢复尝试信息
uint8 current_attempt
uint8 max_attempts

# 失败检测信息
int32 icp_points
float64 weight_sum
float64 translation_norm
float64 icp_score

# 恢复质量信息
float64 recovery_score
float64 wifi_confidence

# 错误信息
string error_message

# 调试信息
bool debug_enabled
string debug_info
