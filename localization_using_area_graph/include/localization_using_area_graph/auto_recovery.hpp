#ifndef AUTO_RECOVERY_HPP
#define AUTO_RECOVERY_HPP

#include <rclcpp/rclcpp.hpp>
#include <chrono>
#include <vector>
#include <memory>
#include <string>

namespace auto_recovery {

/**
 * @brief 定位系统状态枚举
 */
enum class LocalizationState {
    ICP_TRACKING,           // ICP位姿跟踪模式
    FAILURE_DETECTED,       // 检测到跟踪失败
    WIFI_RECOVERY,          // WiFi全局定位恢复中
    RECOVERY_VALIDATION,    // 恢复结果验证
    SMOOTH_TRANSITION       // 平滑过渡到ICP跟踪
};

/**
 * @brief WiFi恢复详细阶段枚举
 */
enum class WiFiRecoveryPhase {
    IDLE,                       // 空闲状态
    TRIGGERING_WIFI_SERVICE,    // 触发WiFi定位服务
    COLLECTING_RSS_DATA,        // 收集RSS数据阶段
    WAITING_WIFI_LOCATION,      // 等待WiFi定位结果
    TRIGGERING_PARTICLE_SERVICE,// 触发粒子生成服务
    GENERATING_PARTICLES,       // 生成粒子阶段
    EXECUTING_GLOBAL_LOC,       // 执行全局定位
    VALIDATING_RESULT,          // 验证恢复结果
    RECOVERY_COMPLETE,          // 恢复完成
    RECOVERY_FAILED            // 恢复失败
};

/**
 * @brief 跟踪质量结构体
 */
struct TrackingQuality {
    int numIcpPoints = 0;
    double weightSumTurkey = 0.0;
    double translationNorm = 0.0;
    double icpScore = 0.0;
    rclcpp::Time timestamp;
    
    TrackingQuality() : timestamp(rclcpp::Time(0)) {}
};

/**
 * @brief 失败检测器类
 */
class FailureDetector {
public:
    struct Config {
        // 即时失败检测阈值
        int min_icp_points = 5;
        double min_weight_sum = 1e-6;
        double max_translation_norm = 5.0;
        
        // 质量退化检测阈值
        double min_point_ratio = 0.05;
        double min_weight_ratio = 0.1;
        double min_icp_score = 0.3;
        
        // 连续失败计数阈值
        int max_critical_failures = 2;
        int max_quality_failures = 5;
        int max_consecutive_poor = 10;
    };

private:
    Config config_;
    int critical_failure_count_ = 0;
    int quality_failure_count_ = 0;
    int consecutive_poor_frames_ = 0;
    std::vector<TrackingQuality> quality_history_;
    size_t max_history_size_ = 20;
    
public:
    explicit FailureDetector(const Config& config) : config_(config) {}
    
    /**
     * @brief 更新跟踪质量并检测失败
     */
    void updateQuality(const TrackingQuality& quality);
    
    /**
     * @brief 检查是否应该触发恢复
     */
    bool shouldTriggerRecovery() const;
    
    /**
     * @brief 重置失败计数器
     */
    void reset();
    
    /**
     * @brief 获取当前失败统计
     */
    void getFailureStats(int& critical, int& quality, int& consecutive) const;

private:
    bool isCriticalFailure(const TrackingQuality& quality) const;
    bool isQualityDegraded(const TrackingQuality& quality) const;
};

/**
 * @brief 自动恢复状态机类
 */
class AutoRecoveryStateMachine {
public:
    struct Config {
        int max_recovery_attempts = 3;
        double recovery_timeout = 30.0;
        double min_recovery_score = 0.5;
        int transition_frames = 5;
        double transition_weight_decay = 0.8;
        double recovery_error_up_thred = 2.0;
        double recovery_error_low_thred = 1.2;
        double threshold_tightening_rate = 0.95;
        bool enable_debug_output = true;
        
        // 新增：分阶段超时配置
        struct AdaptiveTimeout {
            bool enable = false;
            double wifi_collection_timeout = 12.0;
            double particle_generation_timeout = 5.0;
            double global_localization_timeout = 20.0;
            double validation_timeout = 3.0;
            double total_recovery_timeout = 40.0;
            double status_check_interval = 0.1;
        } adaptive_timeout;
        
        // 新增：服务调用超时配置
        struct ServiceTimeout {
            double wifi_service_timeout = 10.0;
            double particle_service_timeout = 5.0;
        } service_timeout;
    };

private:
    Config config_;
    LocalizationState current_state_;
    std::unique_ptr<FailureDetector> failure_detector_;
    
    // 恢复流程状态
    int recovery_attempts_ = 0;
    rclcpp::Time recovery_start_time_;
    rclcpp::Time last_state_change_;
    
    // 新增：WiFi恢复阶段状态追踪
    WiFiRecoveryPhase current_wifi_phase_ = WiFiRecoveryPhase::IDLE;
    rclcpp::Time wifi_phase_start_time_;
    rclcpp::Time last_wifi_status_check_;
    
    // 过渡状态
    int transition_frame_count_ = 0;
    bool odom_fusion_backup_state_ = false;
    
    // 日志记录
    rclcpp::Logger logger_;
    
public:
    explicit AutoRecoveryStateMachine(const Config& config, 
                                    const FailureDetector::Config& detector_config,
                                    rclcpp::Logger logger);
    
    /**
     * @brief 更新状态机
     */
    void update(const TrackingQuality& quality, rclcpp::Time current_time);
    
    /**
     * @brief 获取当前状态
     */
    LocalizationState getCurrentState() const { return current_state_; }
    
    /**
     * @brief 检查是否需要触发WiFi恢复
     */
    bool shouldTriggerWiFiRecovery() const;
    
    /**
     * @brief 检查是否需要验证恢复结果
     */
    bool shouldValidateRecovery() const;
    
    /**
     * @brief 检查是否需要平滑过渡
     */
    bool shouldPerformTransition() const;
    
    /**
     * @brief 通知WiFi恢复完成
     */
    void notifyWiFiRecoveryComplete(double recovery_score);
    
    /**
     * @brief 通知过渡完成
     */
    void notifyTransitionComplete();
    
    /**
     * @brief 重置状态机
     */
    void reset();
    
    /**
     * @brief 获取恢复统计信息
     */
    void getRecoveryStats(int& attempts, double& success_rate) const;
    
    /**
     * @brief 获取当前WiFi恢复阶段
     */
    WiFiRecoveryPhase getCurrentWiFiPhase() const { return current_wifi_phase_; }
    
    /**
     * @brief 设置WiFi恢复阶段
     */
    void setWiFiRecoveryPhase(WiFiRecoveryPhase phase, rclcpp::Time current_time);
    
    /**
     * @brief 检查当前阶段是否超时
     */
    bool isCurrentPhaseTimeout(rclcpp::Time current_time) const;
    
    /**
     * @brief 获取当前阶段的超时时间
     */
    double getCurrentPhaseTimeout() const;

private:
    void changeState(LocalizationState new_state, rclcpp::Time current_time);
    bool isRecoveryTimeout(rclcpp::Time current_time) const;
    void logStateChange(LocalizationState old_state, LocalizationState new_state) const;
    
    // WiFi恢复阶段私有方法
    bool isPhaseTimeout(WiFiRecoveryPhase phase, rclcpp::Time current_time) const;
    double getPhaseTimeout(WiFiRecoveryPhase phase) const;
    std::string wifiPhaseToString(WiFiRecoveryPhase phase) const;
};

/**
 * @brief 自动恢复管理器类 - 主要接口
 */
class AutoRecoveryManager {
public:
    struct Config {
        bool enable_auto_recovery = false;
        FailureDetector::Config detector_config;
        AutoRecoveryStateMachine::Config state_machine_config;
    };

private:
    Config config_;
    std::unique_ptr<AutoRecoveryStateMachine> state_machine_;
    rclcpp::Logger logger_;
    bool enabled_ = false;
    
public:
    explicit AutoRecoveryManager(const Config& config, rclcpp::Logger logger);
    
    /**
     * @brief 启用/禁用自动恢复
     */
    void setEnabled(bool enabled) { enabled_ = enabled; }
    bool isEnabled() const { return enabled_; }
    
    /**
     * @brief 更新跟踪质量并处理恢复逻辑
     */
    void updateTracking(int numIcpPoints, double weightSumTurkey, 
                       double translationNorm, double icpScore, 
                       rclcpp::Time timestamp);
    
    /**
     * @brief 获取当前状态
     */
    LocalizationState getCurrentState() const;
    
    /**
     * @brief 检查各种恢复动作
     */
    bool shouldTriggerWiFiRecovery() const;
    bool shouldValidateRecovery() const;
    bool shouldPerformTransition() const;
    
    /**
     * @brief 通知恢复事件
     */
    void notifyWiFiRecoveryComplete(double recovery_score);
    void notifyTransitionComplete();
    
    /**
     * @brief 重置管理器
     */
    void reset();
    
    /**
     * @brief 获取恢复统计信息
     */
    void getRecoveryStats(int& attempts, double& success_rate) const;
    
    // 新增：WiFi恢复阶段管理方法
    /**
     * @brief 设置WiFi恢复阶段
     */
    void setWiFiRecoveryPhase(WiFiRecoveryPhase phase, rclcpp::Time current_time);
    
    /**
     * @brief 获取当前WiFi恢复阶段
     */
    WiFiRecoveryPhase getCurrentWiFiPhase() const;
    
    /**
     * @brief 检查当前阶段是否超时
     */
    bool isCurrentPhaseTimeout(rclcpp::Time current_time) const;
};

} // namespace auto_recovery

#endif // AUTO_RECOVERY_HPP
